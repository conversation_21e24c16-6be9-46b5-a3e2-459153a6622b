#!/usr/bin/env python3
"""
Exemple pratique d'utilisation de la conversion automatique pour masques avec valeurs 255.

Ce script montre comment :
1. Analyser des masques avec des valeurs 255
2. Les convertir automatiquement au format nnU-Net
3. Vérifier le résultat

Usage:
    python exemple_conversion_255.py
"""

import os
import numpy as np
from PIL import Image
import tempfile
import shutil
from utils.conversion import analyze_masks_format, convert_labels_to_classes

def create_example_dataset():
    """
    Crée un exemple de dataset avec des masques contenant des valeurs 255
    (simule un dataset réel avec ce problème)
    """
    # Créer un dossier temporaire
    temp_dir = tempfile.mkdtemp(prefix="exemple_255_")
    labels_dir = os.path.join(temp_dir, "labelsTr")
    os.makedirs(labels_dir, exist_ok=True)
    
    print(f"📁 Création du dataset d'exemple dans: {labels_dir}")
    
    # Créer plusieurs masques avec des valeurs 255 (format binaire)
    for i in range(1, 6):
        # Masque binaire avec background=0 et objet=255
        mask = np.zeros((128, 128), dtype=np.uint8)
        
        # Ajouter des formes différentes pour chaque masque
        if i == 1:
            # Rectangle
            mask[30:90, 40:80] = 255
        elif i == 2:
            # Cercle approximatif
            center_y, center_x = 64, 64
            y, x = np.ogrid[:128, :128]
            mask_circle = (x - center_x)**2 + (y - center_y)**2 <= 25**2
            mask[mask_circle] = 255
        elif i == 3:
            # Forme en L
            mask[20:100, 20:40] = 255
            mask[80:100, 20:80] = 255
        elif i == 4:
            # Plusieurs petits objets
            mask[20:40, 20:40] = 255
            mask[60:80, 60:80] = 255
            mask[20:40, 80:100] = 255
        else:
            # Forme complexe
            mask[10:50, 10:118] = 255
            mask[50:70, 30:90] = 255
        
        # Sauvegarder le masque
        filename = f"{i:04d}.png"
        Image.fromarray(mask, mode="L").save(os.path.join(labels_dir, filename))
        
        # Afficher les statistiques du masque créé
        unique_vals = np.unique(mask)
        print(f"  ✅ {filename}: valeurs {unique_vals}, pixels non-zéro: {np.sum(mask > 0)}")
    
    return temp_dir, labels_dir

def demonstrate_conversion():
    """
    Démontre le processus complet de conversion automatique
    """
    print("🚀 DÉMONSTRATION DE LA CONVERSION AUTOMATIQUE DES MASQUES 255")
    print("=" * 70)
    
    # 1. Créer le dataset d'exemple
    print("\n📋 Étape 1: Création du dataset d'exemple")
    print("-" * 50)
    temp_dir, labels_dir = create_example_dataset()
    
    try:
        # 2. Analyse du format original
        print("\n📋 Étape 2: Analyse du format original")
        print("-" * 50)
        analyze_masks_format(labels_dir, max_files=5)
        
        # 3. Conversion automatique
        print("\n📋 Étape 3: Conversion automatique")
        print("-" * 50)
        print("🔄 Lancement de la conversion automatique...")
        
        convert_labels_to_classes(labels_dir, auto_detect_binary=True)
        
        # 4. Vérification après conversion
        print("\n📋 Étape 4: Vérification après conversion")
        print("-" * 50)
        analyze_masks_format(labels_dir, max_files=5)
        
        # 5. Comparaison avant/après
        print("\n📋 Étape 5: Comparaison détaillée")
        print("-" * 50)
        
        backup_dir = labels_dir + "_backup"
        if os.path.exists(backup_dir):
            print("📊 Comparaison avant/après conversion:")
            
            for filename in sorted(os.listdir(labels_dir)):
                if filename.endswith('.png'):
                    # Masque original (backup)
                    original_path = os.path.join(backup_dir, filename)
                    original_mask = np.array(Image.open(original_path))
                    original_unique = sorted(np.unique(original_mask))
                    
                    # Masque converti
                    converted_path = os.path.join(labels_dir, filename)
                    converted_mask = np.array(Image.open(converted_path))
                    converted_unique = sorted(np.unique(converted_mask))
                    
                    print(f"  📄 {filename}:")
                    print(f"    Avant:  {original_unique}")
                    print(f"    Après:  {converted_unique}")
                    
                    # Vérifier que la conversion est correcte
                    if set(original_unique) == {0, 255} and set(converted_unique) == {0, 1}:
                        print(f"    ✅ Conversion correcte!")
                    else:
                        print(f"    ⚠️  Conversion inattendue!")
        
        print("\n🎉 DÉMONSTRATION TERMINÉE AVEC SUCCÈS!")
        print("=" * 70)
        print(f"📁 Fichiers de test conservés dans: {temp_dir}")
        print("💡 Vous pouvez examiner les fichiers avant/après conversion.")
        print("🗑️  Supprimez le dossier manuellement quand vous avez terminé.")
        
        return temp_dir
        
    except Exception as e:
        print(f"\n❌ Erreur lors de la démonstration: {e}")
        # Nettoyer en cas d'erreur
        shutil.rmtree(temp_dir, ignore_errors=True)
        raise

def main():
    """Fonction principale"""
    try:
        temp_dir = demonstrate_conversion()
        
        # Demander à l'utilisateur s'il veut nettoyer
        print(f"\n❓ Voulez-vous supprimer les fichiers de test? (y/N): ", end="")
        response = input().strip().lower()
        
        if response in ['y', 'yes', 'oui']:
            shutil.rmtree(temp_dir, ignore_errors=True)
            print("🗑️  Fichiers de test supprimés.")
        else:
            print(f"📁 Fichiers conservés dans: {temp_dir}")
            
    except KeyboardInterrupt:
        print("\n⏹️  Démonstration interrompue par l'utilisateur.")
    except Exception as e:
        print(f"\n❌ Erreur: {e}")

if __name__ == "__main__":
    main()
