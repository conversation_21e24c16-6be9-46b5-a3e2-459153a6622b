#!/usr/bin/env python3
"""
Script utilitaire pour analyser le format des masques dans un dataset.
Utile pour diagnostiquer les problèmes de format avant l'entraînement nnU-Net.

Usage:
    python analyze_masks.py <chemin_vers_dossier_masques>
    
Exemple:
    python analyze_masks.py "C:/path/to/labelsTr"
"""

import sys
import os
from utils.conversion import analyze_masks_format, convert_labels_to_classes

def main():
    if len(sys.argv) != 2:
        print("Usage: python analyze_masks.py <chemin_vers_dossier_masques>")
        print("Exemple: python analyze_masks.py \"C:/path/to/labelsTr\"")
        sys.exit(1)
    
    labels_dir = sys.argv[1]
    
    if not os.path.exists(labels_dir):
        print(f"❌ Erreur: Le dossier '{labels_dir}' n'existe pas.")
        sys.exit(1)
    
    if not os.path.isdir(labels_dir):
        print(f"❌ Erreur: '{labels_dir}' n'est pas un dossier.")
        sys.exit(1)
    
    # Compter les fichiers PNG
    png_files = [f for f in os.listdir(labels_dir) if f.lower().endswith('.png')]
    if not png_files:
        print(f"❌ Erreur: Aucun fichier PNG trouvé dans '{labels_dir}'.")
        sys.exit(1)
    
    print(f"📁 Analyse du dossier: {labels_dir}")
    print(f"📄 Nombre de fichiers PNG trouvés: {len(png_files)}")
    
    # Analyse détaillée
    analyze_masks_format(labels_dir, max_files=min(10, len(png_files)))
    
    # Proposer une conversion automatique
    print("\n🤖 Test de conversion automatique:")
    print("-" * 40)
    
    try:
        # Test de détection sans modification des fichiers
        from utils.conversion import detect_binary_mask_format
        detected_mapping = detect_binary_mask_format(labels_dir)
        
        if detected_mapping:
            print(f"✅ Mapping détecté automatiquement: {detected_mapping}")
            
            response = input("\n❓ Voulez-vous appliquer cette conversion automatiquement? (y/N): ")
            if response.lower() in ['y', 'yes', 'oui']:
                print("\n🔄 Application de la conversion...")
                convert_labels_to_classes(labels_dir, detected_mapping, auto_detect_binary=False)
                print("✅ Conversion terminée!")
                
                # Nouvelle analyse après conversion
                print("\n🔍 Analyse après conversion:")
                analyze_masks_format(labels_dir, max_files=3)
            else:
                print("⏩ Conversion annulée.")
        else:
            print("⚠️  Aucun format binaire standard détecté.")
            print("   Vous devrez utiliser un mapping manuel dans votre script d'entraînement.")
            
    except Exception as e:
        print(f"❌ Erreur lors du test de conversion: {e}")

if __name__ == "__main__":
    main()
