# Changelog - Conversion Automatique des Masques

## 🆕 Version 2.0 - Conversion Automatique des Masques (2025-06-30)

### 🎯 Problème Résolu
- **Issue**: Les masques avec des valeurs 255 (format binaire) n'étaient pas correctement gérés lors de la conversion pour nnU-Net
- **Exemple**: `0000.png: [0, 255]` était ignoré et causait des erreurs d'entraînement
- **Impact**: Nécessitait une intervention manuelle pour chaque dataset avec ce format

### ✅ Nouvelles Fonctionnalités

#### 1. Détection Automatique des Formats Binaires
- **Fonction**: `detect_binary_mask_format()`
- **Capacités**:
  - Détecte automatiquement les formats binaires `{0, 255}` → `{0: 0, 255: 1}`
  - Gère les formats binaires personnalisés `{0, 128}` → `{0: 0, 128: 1}`
  - Véri<PERSON> la cohérence sur tous les fichiers du dataset
  - Échoue proprement pour les formats mixtes ou incohérents

#### 2. Conversion Intelligente Améliorée
- **Fonction**: `convert_labels_to_classes()` (mise à jour)
- **Nouvelles options**:
  - `auto_detect_binary=True` (par défaut) : détection automatique activée
  - Fallback vers mapping manuel si la détection échoue
  - Logs détaillés des conversions avant/après
  - Backup automatique des fichiers originaux

#### 3. Outils de Diagnostic Avancés
- **Fonction**: `analyze_masks_format()`
  - Analyse détaillée des formats de masques
  - Statistiques par format détecté
  - Suggestions de conversion automatiques
  - Support pour datasets volumineux (analyse par échantillonnage)

### 🛠️ Nouveaux Scripts et Outils

#### 1. Script d'Analyse Standalone
- **Fichier**: `analyze_masks.py`
- **Usage**: `python analyze_masks.py "chemin/vers/labelsTr"`
- **Fonctionnalités**:
  - Analyse non-destructive des masques
  - Conversion interactive optionnelle
  - Validation post-conversion

#### 2. Script de Démonstration
- **Fichier**: `exemple_conversion_255.py`
- **Usage**: `python exemple_conversion_255.py`
- **Fonctionnalités**:
  - Crée un dataset d'exemple avec valeurs 255
  - Démontre le processus complet de conversion
  - Comparaison avant/après avec validation

#### 3. Suite de Tests Automatisés
- **Fichier**: `test_conversion.py`
- **Usage**: `python test_conversion.py`
- **Tests inclus**:
  - Détection automatique binaire standard
  - Gestion des formats mixtes (échec attendu)
  - Conversion manuelle avec mapping personnalisé
  - Validation de l'intégrité des conversions

### 🔄 Scripts Existants Mis à Jour

#### 1. Script d'Entraînement (`train_nnunet.py`)
- **Avant**:
  ```python
  convert_labels_to_classes(labels_dir, manual_mapping)
  ```
- **Après**:
  ```python
  # Analyse préliminaire
  analyze_masks_format(labels_dir, max_files=3)
  
  # Conversion avec fallback automatique
  try:
      convert_labels_to_classes(labels_dir, auto_detect_binary=True)
  except ValueError:
      convert_labels_to_classes(labels_dir, manual_mapping, auto_detect_binary=False)
  ```

### 📊 Formats Supportés

| Format | Valeurs | Détection | Conversion | Status |
|--------|---------|-----------|------------|--------|
| Binaire standard | `{0, 255}` | ✅ Auto | `{0: 0, 255: 1}` | ✅ |
| Binaire personnalisé | `{0, 128}` | ✅ Auto | `{0: 0, 128: 1}` | ✅ |
| Classes nnU-Net | `{0, 1, 2, 3}` | ✅ Auto | Aucune nécessaire | ✅ |
| Multi-classes | `{0, 29, 149, 255}` | ⚠️ Manuel | Mapping requis | ✅ |
| Formats mixtes | `{0, 128, 255}` | ❌ Échec | Mapping requis | ✅ |

### 🎉 Avantages

1. **Automatisation**: Plus besoin d'intervention manuelle pour les formats binaires courants
2. **Robustesse**: Fallback intelligent vers conversion manuelle
3. **Transparence**: Logs détaillés et outils de diagnostic
4. **Sécurité**: Backup automatique avant toute modification
5. **Flexibilité**: Support des formats existants + nouveaux formats automatiques

### 📚 Documentation Ajoutée

- `doc/conversion_automatique.md` : Guide complet de la nouvelle fonctionnalité
- `README.md` : Mise à jour avec les nouvelles fonctionnalités
- `CHANGELOG.md` : Ce fichier de changelog détaillé

### 🚀 Migration

**Pour les utilisateurs existants** :
- Aucune modification requise dans les scripts existants
- La détection automatique est activée par défaut
- Les mappings manuels continuent de fonctionner comme avant

**Pour les nouveaux utilisateurs** :
1. Analysez vos masques : `python analyze_masks.py "path/to/labelsTr"`
2. Lancez l'entraînement : `python train_nnunet.py`
3. La conversion se fait automatiquement si possible

### 🔮 Prochaines Améliorations Possibles

- Support des formats TIFF et autres formats d'images
- Détection automatique des formats multi-classes complexes
- Interface graphique pour l'analyse et la conversion
- Intégration avec d'autres frameworks de segmentation
