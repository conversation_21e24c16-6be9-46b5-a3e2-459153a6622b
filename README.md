# nnU-Net pour la Segmentation d'Images de Corrosion

## Description
Ce projet implémente la segmentation d'images de corrosion en utilisant nnU-Net, un framework de deep learning spécialisé dans la segmentation d'images médicales. Le projet est configuré pour détecter et segmenter différentes classes de corrosion :
- Frontwall (29)
- Backwall (149)
- <PERSON>law (76)
- Indication (125)

**🆕 Nouveauté** : Détection et conversion automatique des masques binaires avec valeurs 255.

## Structure du Projet
```
unet/
├── utils/                 # Fonctions utilitaires
│   ├── conversion.py      # Conversion des images et labels + détection auto masques 255
│   ├── overlay_manager.py # Gestion des overlays et visualisations
│   ├── rename.py          # Renommage des fichiers
│   ├── verification.py    # Vérification de l'intégrité des données
│   └── visualisation.py   # Outils de visualisation
├── train_nnunet.py        # Script d'entraînement nnU-Net
├── infer_nnunet.py        # Script d'inférence nnU-Net
├── export_model_zip.py    # Export du modèle entraîné
└── doc/resumé_nnunet.md   # Résumé des commandes nnU-Net
```

## Prérequis
- Python 3.8+
- nnU-Net v2
- PyTorch
- NumPy
- OpenCV
- Matplotlib

## Configuration des Variables d'Environnement
```bash
export nnUNet_raw="chemin/vers/nnUNet_raw"
export nnUNet_preprocessed="chemin/vers/nnUNet_preprocessed"
export nnUNet_results="chemin/vers/nnUNet_results"
```

## Utilisation

### Entraînement
```bash
python train_nnunet.py
```
Le script d'entraînement :
- Prétraite les images (conversion en niveaux de gris)
- **🆕 Convertit automatiquement les masques binaires (0, 255) → (0, 1)**
- Fallback vers conversion manuelle pour formats complexes
- Planifie et préprocesse le dataset
- Entraîne le modèle avec la configuration spécifiée

#### 🆕 Conversion Automatique des Masques 255
Le script détecte automatiquement les masques binaires avec des valeurs 255 et les convertit au format nnU-Net :

**Formats supportés automatiquement :**
- `{0, 255}` → `{0, 1}` (format binaire standard)
- `{0, 128}` → `{0, 1}` (format binaire personnalisé)

**Formats nécessitant un mapping manuel :**
- Formats multi-classes : `{0, 29, 149, 255}` → mapping personnalisé requis
- Formats mixtes ou incohérents

**Exemple d'utilisation :**
```python
# Conversion automatique (recommandée)
convert_labels_to_classes(labels_dir, auto_detect_binary=True)

# Conversion manuelle (fallback)
convert_labels_to_classes(labels_dir, {
    0: 0,    # background
    29: 1,   # frontwall
    149: 2,  # backwall
    76: 3,   # flaw
    125: 4   # indication
}, auto_detect_binary=False)
```

### Inférence
```bash
python infer_nnunet.py
```
Le script d'inférence :
- Prétraite les images d'entrée
- Effectue la prédiction
- Post-traite les masques prédits
- Génère des visualisations avec overlays

### Export du Modèle
```bash
python export_model_zip.py
```
Le script d'export :
- Exporte le modèle entraîné au format ZIP
- Inclut les métadonnées et configurations nécessaires
- Prépare le modèle pour le déploiement

### Visualisation
Les outils de visualisation sont disponibles dans le module `utils/` :
- `overlay_manager.py` : Gestion des overlays et visualisations avancées
- `visualisation.py` : Outils de visualisation de base

## Configuration
Les paramètres principaux sont configurables dans les scripts :
- `DATASET_ID` : Identifiant du dataset
- `CONFIGURATION` : Configuration du modèle (2d ou 3d_fullres)
- `FOLD` : Fold pour la validation croisée
- `EPOCHS` : Nombre d'époques d'entraînement
- `GPU_ID` : ID du GPU à utiliser

## Fonctionnalités
- Implémentation nnU-Net v2 pour la segmentation d'images de corrosion
- **🆕 Détection et conversion automatique des masques binaires (0, 255)**
- Pipeline complet de prétraitement et post-traitement
- Gestion avancée des overlays et visualisations
- Export du modèle entraîné pour déploiement
- Support multi-GPU
- Outils de vérification et validation des données

## Avantages de la Conversion Automatique
- **Généralisation** : Gère automatiquement les formats binaires courants avec 255
- **Robustesse** : Fallback intelligent vers conversion manuelle si nécessaire
- **Simplicité** : Plus besoin d'intervention manuelle pour les datasets binaires
- **Sécurité** : Backup automatique des fichiers originaux avant conversion
- **Compatibilité** : Fonctionne avec les scripts existants sans modification

## Support
Pour toute question ou problème, veuillez créer une issue dans le repository GitLab.

## Licence
Propriétaire - Tous droits réservés
