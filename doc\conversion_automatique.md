# Conversion Automatique des Masques pour nnU-Net

## 🎯 Problème Résolu

Lors de l'entraînement nnU-Net, les masques doivent être au format classes entières (0, 1, 2, 3...). Cependant, beaucoup de datasets utilisent des valeurs arbitraires comme 255 pour représenter les classes, ce qui causait des erreurs lors de la conversion.

**Exemple de problème :**
```
0000.png: [0, 255]  # Format binaire avec 255
```

Le script ignorait ces valeurs 255 et ne les convertissait pas correctement.

## ✅ Solution Implémentée

### 1. Détection Automatique des Masques Binaires

La fonction `detect_binary_mask_format()` détecte automatiquement :
- Format binaire standard : `{0, 255}` → `{0: 0, 255: 1}`
- Format binaire personnalisé : `{0, 128}` → `{0: 0, 128: 1}`
- Formats incohérents ou multi-classes (pas de conversion automatique)

### 2. Conversion Intelligente

La fonction `convert_labels_to_classes()` a été améliorée avec :
- **Détection automatique** activée par défaut (`auto_detect_binary=True`)
- **Fallback manuel** si la détection échoue
- **Analyse détaillée** des valeurs avant/après conversion

### 3. Outils de Diagnostic

#### Script d'analyse : `analyze_masks.py`
```bash
python analyze_masks.py "C:/path/to/labelsTr"
```

**Fonctionnalités :**
- Analyse détaillée du format des masques
- Suggestions de conversion automatiques
- Option de conversion interactive

#### Fonction d'analyse : `analyze_masks_format()`
```python
from utils.conversion import analyze_masks_format
analyze_masks_format("path/to/masks", max_files=5)
```

## 🚀 Utilisation

### 1. Conversion Automatique (Recommandée)

```python
from utils.conversion import convert_labels_to_classes

# Détection et conversion automatiques
convert_labels_to_classes(labels_dir, auto_detect_binary=True)
```

### 2. Conversion Manuelle (Fallback)

```python
# Mapping manuel pour formats complexes
convert_labels_to_classes(labels_dir, {
    0: 0,    # background
    29: 1,   # frontwall
    149: 2,  # backwall
    76: 3,   # flaw
    125: 4   # indication
}, auto_detect_binary=False)
```

### 3. Script d'Entraînement Mis à Jour

Le script `train_nnunet.py` utilise maintenant :

```python
# Analyse des masques avant conversion
analyze_masks_format(labels_dir, max_files=3)

# Conversion avec fallback automatique
try:
    convert_labels_to_classes(labels_dir, auto_detect_binary=True)
except ValueError:
    # Fallback vers mapping manuel
    convert_labels_to_classes(labels_dir, manual_mapping, auto_detect_binary=False)
```

## 📊 Formats Supportés

| Format Détecté | Valeurs | Conversion | Status |
|----------------|---------|------------|--------|
| Binaire standard | `{0, 255}` | `{0: 0, 255: 1}` | ✅ Auto |
| Binaire personnalisé | `{0, 128}` | `{0: 0, 128: 1}` | ✅ Auto |
| Classes nnU-Net | `{0, 1, 2, 3}` | Aucune | ✅ Détecté |
| Multi-classes | `{0, 29, 149, 255}` | Manuel requis | ⚠️ Manuel |
| Formats mixtes | `{0, 128, 255}` | Manuel requis | ⚠️ Manuel |

## 🔧 Tests et Validation

### Script de Test
```bash
python test_conversion.py
```

**Tests inclus :**
- ✅ Détection automatique binaire (0, 255)
- ✅ Gestion des formats mixtes (échec attendu)
- ✅ Conversion manuelle avec mapping personnalisé
- ✅ Vérification de l'intégrité après conversion

### Exemple de Sortie
```
🔍 Analyse 0001.png: valeurs uniques [0, 255]
✅ Format binaire standard détecté (0, 255) - cohérent sur tous les fichiers
🎯 Mapping détecté: {0: 0, 255: 1}
✅ Masque converti (classes) : 0001.png - {0, 255} → {0, 1}
```

## 🛠️ Workflow Recommandé

1. **Analyse préliminaire :**
   ```bash
   python analyze_masks.py "path/to/labelsTr"
   ```

2. **Entraînement avec conversion automatique :**
   ```bash
   python train_nnunet.py
   ```

3. **Si problème, conversion manuelle :**
   ```python
   convert_labels_to_classes(labels_dir, custom_mapping, auto_detect_binary=False)
   ```

## 🎉 Avantages

- ✅ **Généralisation** : Gère automatiquement les formats binaires avec 255
- ✅ **Robustesse** : Fallback manuel si détection échoue
- ✅ **Diagnostic** : Outils d'analyse détaillée
- ✅ **Sécurité** : Backup automatique avant conversion
- ✅ **Transparence** : Logs détaillés des conversions

Cette amélioration rend le pipeline nnU-Net beaucoup plus flexible et capable de gérer une variété de formats de masques sans intervention manuelle.
