#!/usr/bin/env python3
"""
Script de test pour la nouvelle fonctionnalité de conversion automatique des masques.
Crée des masques de test avec différents formats et teste la conversion.
"""

import os
import numpy as np
from PIL import Image
import tempfile
import shutil
from utils.conversion import analyze_masks_format, convert_labels_to_classes, detect_binary_mask_format

def create_test_masks(test_dir):
    """Crée des masques de test avec différents formats"""
    os.makedirs(test_dir, exist_ok=True)
    
    # Masque binaire standard (0, 255)
    mask1 = np.zeros((100, 100), dtype=np.uint8)
    mask1[20:80, 20:80] = 255
    Image.fromarray(mask1, mode="L").save(os.path.join(test_dir, "0001.png"))
    
    # Masque binaire avec autre valeur (0, 255)
    mask2 = np.zeros((100, 100), dtype=np.uint8)
    mask2[30:70, 30:70] = 255
    Image.fromarray(mask2, mode="L").save(os.path.join(test_dir, "0002.png"))
    
    # Masque binaire avec valeurs différentes (0, 128)
    mask3 = np.zeros((100, 100), dtype=np.uint8)
    mask3[40:60, 40:60] = 128
    Image.fromarray(mask3, mode="L").save(os.path.join(test_dir, "0003.png"))
    
    print(f"✅ Masques de test créés dans: {test_dir}")
    return test_dir

def test_binary_detection():
    """Test de la détection automatique des masques binaires"""
    print("\n" + "="*60)
    print("🧪 TEST DE DÉTECTION AUTOMATIQUE DES MASQUES BINAIRES")
    print("="*60)
    
    # Créer un dossier temporaire
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = os.path.join(temp_dir, "test_masks")
        
        # Test 1: Masques binaires (0, 255)
        print("\n📋 Test 1: Masques binaires standard (0, 255)")
        print("-" * 50)
        
        create_test_masks(test_dir)
        
        # Supprimer le masque avec valeurs différentes pour ce test
        os.remove(os.path.join(test_dir, "0003.png"))
        
        # Analyse
        analyze_masks_format(test_dir, max_files=5)
        
        # Test de détection
        detected_mapping = detect_binary_mask_format(test_dir)
        print(f"\n🎯 Mapping détecté: {detected_mapping}")
        
        if detected_mapping:
            print("✅ Détection automatique réussie!")
            
            # Test de conversion
            print("\n🔄 Test de conversion automatique...")
            convert_labels_to_classes(test_dir, auto_detect_binary=True)
            
            # Vérification après conversion
            print("\n🔍 Vérification après conversion:")
            analyze_masks_format(test_dir, max_files=5)
        else:
            print("❌ Détection automatique échouée")
        
        # Nettoyer pour le test suivant
        shutil.rmtree(test_dir)

def test_mixed_formats():
    """Test avec des formats mixtes (devrait échouer la détection automatique)"""
    print("\n" + "="*60)
    print("🧪 TEST AVEC FORMATS MIXTES")
    print("="*60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = os.path.join(temp_dir, "test_masks_mixed")
        
        print("\n📋 Test 2: Formats mixtes (devrait échouer)")
        print("-" * 50)
        
        create_test_masks(test_dir)  # Inclut le masque avec valeurs (0, 128)
        
        # Analyse
        analyze_masks_format(test_dir, max_files=5)
        
        # Test de détection (devrait échouer)
        detected_mapping = detect_binary_mask_format(test_dir)
        print(f"\n🎯 Mapping détecté: {detected_mapping}")
        
        if detected_mapping is None:
            print("✅ Détection automatique correctement échouée pour formats mixtes!")
        else:
            print("⚠️  Détection automatique inattendue pour formats mixtes")

def test_manual_conversion():
    """Test de conversion manuelle"""
    print("\n" + "="*60)
    print("🧪 TEST DE CONVERSION MANUELLE")
    print("="*60)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = os.path.join(temp_dir, "test_masks_manual")
        
        print("\n📋 Test 3: Conversion manuelle avec mapping personnalisé")
        print("-" * 50)
        
        create_test_masks(test_dir)
        
        # Analyse avant conversion
        print("🔍 Avant conversion:")
        analyze_masks_format(test_dir, max_files=5)
        
        # Conversion manuelle avec mapping personnalisé
        manual_mapping = {0: 0, 255: 1, 128: 2}
        print(f"\n🔄 Application du mapping manuel: {manual_mapping}")
        
        convert_labels_to_classes(test_dir, manual_mapping, auto_detect_binary=False)
        
        # Analyse après conversion
        print("\n🔍 Après conversion:")
        analyze_masks_format(test_dir, max_files=5)

def main():
    """Fonction principale de test"""
    print("🚀 DÉMARRAGE DES TESTS DE CONVERSION DE MASQUES")
    
    try:
        test_binary_detection()
        test_mixed_formats()
        test_manual_conversion()
        
        print("\n" + "="*60)
        print("✅ TOUS LES TESTS TERMINÉS AVEC SUCCÈS!")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ Erreur lors des tests: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
